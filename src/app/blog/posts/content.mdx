---
title: "Updating the content of Magic Portfolio"
summary: "Personalize the portfolio template with your own content."
publishedAt: "2025-04-18"
tag: "Magic Portfolio"
---

## Avatar

Replace the `public/images/avatar.png` file with your own avatar image. It is used on the `/about` page as well as on the dynamically generated `open-graph` images.

## Favicon

Replace the `src/app/favicon.ico` file with your own favicon.

## Content

Replace the content in the `resources/content.js` file with your own content. You can use custom components in most cases, since the props are usually declared as `ReactNode` instead of `string`, but it's important that you need to import them in the file.

<CodeBlock
    marginBottom="16"
    highlight="1-2"
    codes={[
  {
    code:
`import { InlineCode } from "@once-ui-system/core";
import Link from "next/link";

const person = {
  ...
};`,
    language: "tsx",
    label: "src/app/resources/content.js"
  }
]} />

The imports above would let us use `InlineCode` and `Link` components in the content file.

### Personal details

Your personal details are used across the whole app to render personalized headings, labels and images. Languages are displayed on the `/about` page, and location and time in the header.

<CodeBlock
    marginBottom="16"
    highlight="2-3,7-10"
    codes={[
  {
    code:
`const person = {
    firstName: "Selene",
    lastName: "Yu",
    get name() {
      return "...";
    },
    role: "Design Engineer",
    avatar: "/images/avatar.png",
    location: "Asia/Jakarta", // Expecting the IANA time zone identifier, e.g., 'Europe/Vienna'
    languages: ["English", "Bahasa"], // optional: Leave the array empty if you don't want to display languages
};`,
    language: "tsx",
    label: "src/app/resources/content.js"
  }
]} />

### Social links

Social links are rendered on the `/about` page and in the footer based on the array configured in the `social` object. You can set custom icons to each, but don't forget to import them in `src/once-ui/icons.ts`. Read the Once UI documentation](https://once-ui.com/docs/icons) for more information.

<CodeBlock
    marginBottom="16"
    codes={[
  {
    code:
`const social = [
    {
      name: "GitHub",
      icon: "github",
      link: "https://github.com/once-ui-system/nextjs-starter",
    },
    {
      name: "Email",
      icon: "email",
      link: "mailto:<EMAIL>",
    },
];`,
    language: "tsx",
    label: "src/app/resources/content.js"
  }
]} />

### Pages

Each page have it's own object that manages the personalized content, such as headings, subheadings, images and dynamic data.

<CodeBlock
    marginBottom="16"
    codes={[
  {
    code:
`const home = {
    label: "Home",
    title: "Selene Yu's Portfolio",
    description: "Portfolio website showcasing my work as a Design Engineer",
    headline: <>Design engineer and builder</>,
    subline: (
      <>
        I'm Selene, a design engineer at <InlineCode>FLY</InlineCode>, where I craft intuitive
        <br /> user experiences. After hours, I build my own projects.
      </>
    ),
};`,
    language: "tsx",
    label: "src/app/resources/content.js"
  }
]} />

The `label` property is used in the header navigation. The `title` property is used for the page title and the `description` property is used for the page description. They are also used in meta- and open graph tags.