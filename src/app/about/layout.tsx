import { Meta } from "@once-ui-system/core";
import { baseURL } from "@/resources";

export async function generateMetadata() {
  return Meta.generate({
    title: "About <PERSON> <PERSON><PERSON><PERSON>",
    description: "Meet <PERSON><PERSON><PERSON>, Software Engineer III from Asia/Dhaka",
    baseURL: baseURL,
    image: `/api/og/generate?title=${encodeURIComponent("About <PERSON> Tan<PERSON><PERSON> Ahmed")}`,
    path: "/about",
  });
}

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
