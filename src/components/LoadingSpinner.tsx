import { Column, Flex, Text } from "@once-ui-system/core";

interface LoadingSpinnerProps {
  message?: string;
}

export function LoadingSpinner({ message = "Loading..." }: LoadingSpinnerProps) {
  return (
    <Column maxWidth="m" fillWidth horizontal="center" vertical="center" minHeight="400">
      <Flex vertical="center" horizontal="center" gap="m">
        <div className="loading-spinner">
          <div className="spinner"></div>
        </div>
        <Text variant="body-default-m" onBackground="neutral-weak">
          {message}
        </Text>
      </Flex>
      
      <style jsx>{`
        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        
        .spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(255, 255, 255, 0.1);
          border-top: 3px solid var(--brand-medium);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </Column>
  );
}

interface ErrorMessageProps {
  message: string;
  onRetry?: () => void;
}

export function ErrorMessage({ message, onRetry }: ErrorMessageProps) {
  return (
    <Column maxWidth="m" fillWidth horizontal="center" vertical="center" minHeight="400" gap="m">
      <Text variant="heading-strong-l" onBackground="danger-medium">
        Error Loading Data
      </Text>
      <Text variant="body-default-m" onBackground="neutral-weak" textAlign="center">
        {message}
      </Text>
      {onRetry && (
        <button 
          onClick={onRetry}
          style={{
            padding: '12px 24px',
            backgroundColor: 'var(--brand-medium)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          Try Again
        </button>
      )}
    </Column>
  );
}
