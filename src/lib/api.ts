import { 
  PersonData, 
  SocialMediaData, 
  ExperienceData, 
  EducationData, 
  SkillData, 
  ConfigData,
  AboutPageData,
  ApiResponse 
} from '@/types/api';

// Generic fetch function with error handling
async function fetchApi<T>(endpoint: string): Promise<T> {
  try {
    const response = await fetch(`/api${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Ensure fresh data on each request
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error);
    throw error;
  }
}

// Individual API functions
export async function fetchPersonData(): Promise<PersonData> {
  return fetchApi<PersonData>('/user');
}

export async function fetchBioData(): Promise<any> {
  return fetchApi<any>('/bio');
}

export async function fetchSocialMediaData(): Promise<SocialMediaData[]> {
  return fetchApi<SocialMediaData[]>('/social-media');
}

export async function fetchExperienceData(): Promise<ExperienceData[]> {
  return fetchApi<ExperienceData[]>('/company');
}

export async function fetchEducationData(): Promise<EducationData[]> {
  return fetchApi<EducationData[]>('/education');
}

export async function fetchSkillData(): Promise<SkillData[]> {
  return fetchApi<SkillData[]>('/skill');
}

export async function fetchConfigData(): Promise<ConfigData> {
  return fetchApi<ConfigData>('/config');
}

// Combined function to fetch all about page data
export async function fetchAboutPageData(): Promise<AboutPageData> {
  try {
    const [
      person,
      bio,
      social,
      experiences,
      education,
      skills,
      config
    ] = await Promise.all([
      fetchPersonData(),
      fetchBioData(),
      fetchSocialMediaData(),
      fetchExperienceData(),
      fetchEducationData(),
      fetchSkillData(),
      fetchConfigData()
    ]);

    return {
      person,
      social,
      experiences,
      education,
      skills,
      config
    };
  } catch (error) {
    console.error('Error fetching about page data:', error);
    throw error;
  }
}

// Fallback data transformation functions
export function transformPersonData(apiData: any): PersonData {
  return {
    firstName: apiData.firstName || apiData.first_name || '',
    lastName: apiData.lastName || apiData.last_name || '',
    name: apiData.name || `${apiData.firstName || apiData.first_name || ''} ${apiData.lastName || apiData.last_name || ''}`,
    role: apiData.role || apiData.position || '',
    avatar: apiData.avatar || apiData.profile_image || '/images/avatar.png',
    email: apiData.email || '',
    location: apiData.location || apiData.timezone || '',
    languages: apiData.languages || []
  };
}

export function transformExperienceData(apiData: any[]): ExperienceData[] {
  return apiData.map(exp => ({
    company: exp.company || exp.company_name || '',
    timeframe: exp.timeframe || exp.duration || '',
    role: exp.role || exp.position || '',
    achievements: exp.achievements || exp.responsibilities || [],
    images: exp.images || []
  }));
}

export function transformEducationData(apiData: any[]): EducationData[] {
  return apiData.map(edu => ({
    name: edu.name || edu.institution || '',
    description: edu.description || edu.degree || ''
  }));
}

export function transformSkillData(apiData: any[]): SkillData[] {
  return apiData.map(skill => ({
    title: skill.title || skill.name || '',
    description: skill.description || '',
    images: skill.images || []
  }));
}
