'use client';

import { useState, useEffect } from 'react';
import { AboutPageData } from '@/types/api';
import { 
  fetchAboutPageData,
  transformPersonData,
  transformExperienceData,
  transformEducationData,
  transformSkillData
} from '@/lib/api';

interface UseAboutDataReturn {
  data: AboutPageData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useAboutData(): UseAboutDataReturn {
  const [data, setData] = useState<AboutPageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const aboutData = await fetchAboutPageData();
      
      // Transform data if needed to match expected format
      const transformedData: AboutPageData = {
        ...aboutData,
        person: transformPersonData(aboutData.person),
        experiences: transformExperienceData(aboutData.experiences),
        education: transformEducationData(aboutData.education),
        skills: transformSkillData(aboutData.skills)
      };
      
      setData(transformedData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
      console.error('Error in useAboutData:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}
