// API Response Types
export interface PersonData {
  firstName: string;
  lastName: string;
  name: string;
  role: string;
  avatar: string;
  email: string;
  location: string;
  languages: string[];
}

export interface SocialMediaData {
  name: string;
  icon: string;
  link: string;
}

export interface ExperienceData {
  company: string;
  timeframe: string;
  role: string;
  achievements: string[];
  images: {
    src: string;
    alt: string;
    width: number;
    height: number;
  }[];
}

export interface EducationData {
  name: string;
  description: string;
}

export interface SkillData {
  title: string;
  description: string;
  images?: {
    src: string;
    alt: string;
    width: number;
    height: number;
  }[];
}

export interface ConfigData {
  about: {
    path: string;
    label: string;
    title: string;
    description: string;
    tableOfContent: {
      display: boolean;
      subItems: boolean;
    };
    avatar: {
      display: boolean;
    };
    calendar: {
      display: boolean;
      link: string;
    };
    intro: {
      display: boolean;
      title: string;
      description: string;
    };
    work: {
      display: boolean;
      title: string;
    };
    studies: {
      display: boolean;
      title: string;
    };
    technical: {
      display: boolean;
      title: string;
    };
  };
}

// Combined data interface for the About page
export interface AboutPageData {
  person: PersonData;
  social: SocialMediaData[];
  experiences: ExperienceData[];
  education: EducationData[];
  skills: SkillData[];
  config: ConfigData;
}

// API Response wrappers
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
